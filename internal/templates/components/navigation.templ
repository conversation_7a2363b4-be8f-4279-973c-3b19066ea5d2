package components

import "server-panel/internal/i18n"
import "server-panel/internal/version"

templ Navigation(username, currentLang string) {
	<!-- 导航栏 -->
	<nav class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700 fixed w-full top-0 z-40">
		<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
			<div class="flex justify-between h-16">
				<div class="flex">
					<!-- Logo -->
					<div class="flex-shrink-0 flex items-center">
						<div class="flex items-center space-x-3">
							<div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
								<span class="text-white font-bold text-sm">D</span>
							</div>
							<div class="flex items-center space-x-2">
								<span class="text-xl font-bold text-gray-900 dark:text-gray-100">DigWis</span>
								<span class="text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded-full font-medium">
									{ version.GetDisplayVersion() }
								</span>
							</div>
							<span class="text-sm text-gray-500 dark:text-gray-400 hidden sm:block">{ i18n.T(currentLang, "nav.subtitle") }</span>
						</div>
					</div>

					<!-- 主导航 -->
					<div class="hidden sm:ml-6 sm:flex sm:space-x-8" x-data="navigation" x-init="setActiveNav()">
						<a href="/dashboard"
						   :class="isActive('/dashboard') ? 'border-blue-500 text-gray-900 dark:text-gray-100' : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'"
						   class="inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium transition-colors duration-200">
							{ i18n.T(currentLang, "nav.dashboard") }
						</a>
						<a href="/projects"
						   :class="isActive('/projects') ? 'border-blue-500 text-gray-900 dark:text-gray-100' : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'"
						   class="inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium transition-colors duration-200">
							{ i18n.T(currentLang, "nav.projects") }
						</a>
						<a href="/environment"
						   :class="isActive('/environment') ? 'border-blue-500 text-gray-900 dark:text-gray-100' : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'"
						   class="inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium transition-colors duration-200">
							{ i18n.T(currentLang, "nav.environment") }
						</a>
					</div>
				</div>

				<!-- 右侧菜单 -->
				<div class="hidden sm:ml-6 sm:flex sm:items-center">
					<div class="flex items-center space-x-4 mr-4">
						<div class="flex items-center space-x-2">
							<div class="w-2 h-2 bg-green-400 rounded-full"></div>
							<span class="text-sm text-gray-600 dark:text-gray-400">{ i18n.T(currentLang, "system.status.normal") }</span>
						</div>
					</div>

					<!-- 语言切换器 -->
					<div class="mr-4">
						@LanguageSwitcher(currentLang)
					</div>

					<!-- 主题切换按钮 -->
					<div class="mr-4">
						<button
							@click="toggleTheme()"
							class="p-2 rounded-lg bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 transition-colors duration-200"
							title="切换主题"
						>
							<!-- 太阳图标 (浅色模式时显示) -->
							<svg x-show="!isDark" class="w-5 h-5 text-gray-600 dark:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
							</svg>
							<!-- 月亮图标 (暗黑模式时显示) -->
							<svg x-show="isDark" class="w-5 h-5 text-gray-600 dark:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
							</svg>
						</button>
					</div>

					<!-- 用户菜单 -->
					<div class="ml-3 relative" x-data="{ open: false }">
						<button @click="open = !open" class="bg-white rounded-full flex text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
							<div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
								<svg class="w-5 h-5 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
									<path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
								</svg>
							</div>
						</button>

						<div x-show="open" @click.away="open = false" x-transition class="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5">
							<div class="py-1">
								<div class="px-4 py-2 text-sm text-gray-700 dark:text-gray-300 border-b border-gray-100 dark:border-gray-600">
									<div class="font-medium">{ username }</div>
									<div class="text-gray-500 dark:text-gray-400">{ i18n.T(currentLang, "nav.admin") }</div>
								</div>
								<a href="/logout" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">{ i18n.T(currentLang, "nav.logout") }</a>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</nav>
}
